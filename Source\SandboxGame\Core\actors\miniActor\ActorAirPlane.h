#ifndef __ACTOR_AIRPLANE_H__
#define __ACTOR_AIRPLANE_H__

#include "actors/clientActor/ClientActor.h"

class ActorAirPlane : public ClientActor //tolua_exports
{ //tolua_exports
	DECLARE_SCENEOBJECTCLASS(ActorAirPlane)
public:
	//tolua_begin
	ActorAirPlane();
	void init(int itemid);
	//tolua_end
	 
	void createEvent();
	virtual flatbuffers::Offset<FBSave::SectionActor> save(SAVE_BUFFER_BUILDER &builder) override;
	virtual bool load(const void *srcdata, int version) override;
	//tolua_begin
	static ActorAirPlane *create(int itemid, World *pworld, int x, int y, int z);
	//tolua_end
	virtual bool interact(ClientActor *player, bool onshift=false, bool isMobile=false) override;
	//virtual void onCull(MINIW::CullResult *presult, MINIW::CullFrustum *frustum);
	virtual void tick();
	virtual void update(float dtime);
	virtual int getObjType()const override;
	virtual bool managedByChunk() override { return false; } //����chunk�йܣ�����chunk��ɾ��ʱ��actorҲ��ɾ��
	virtual bool needWalkEffects()
	{
		return false;
	}
	virtual bool preventActorSpawning()
	{
		return true;
	}
	virtual bool canBeCollidedWith()
	{
		return !needClear();
	}
	virtual bool canBePushed()
	{
		return true;
	}
	//tolua_begin

	 
 
	bool m_HostMotivate;
	//tolua_end
	virtual void enterWorld(World* pworld);
	virtual void leaveWorld(bool keep_inchunk);
 
 
 
	virtual int getDefID()
	{
		return m_ItemID;
	}
protected:
	virtual ~ActorAirPlane();

	int m_ItemID;
	//int m_NumRidePos;
	//WORLD_ID m_OtherRiddens[2];
	Rainbow::Model *m_Model;
}; //tolua_exports

#endif
