#include "AirDropCraft.h"
#include "WorldManager.h"
#include "GameNetManager.h"
#include "world.h"
#include "ActorManagerInterface.h"
#include "ClientActorManager.h"
#include "ClientItem.h"
#include "ActorLocoMotion.h"
#include "ActorBoat.h"
#include "IClientGameManagerInterface.h"
#include "SoundComponent.h"

#include <cmath>
#include <time.h>

#define SoundPath "storehorse.aircraft"

AirDropCraft::AirDropCraft(const WorldEventDef& def, const Rainbow::Vector3f& startPos, const Rainbow::Vector3f& endPos)
    : m_event_def(def)
    , m_startPos(startPos)
    , m_endPos(endPos)
{
    m_lastTime = 0.0;
    m_startTime = 0.0;
    m_currentPos = startPos;
    m_speed = m_event_def.Speed;

    // 播放特效
    PlayEffects();

    if (g_WorldMgr && g_WorldMgr->getWorld(0)) {
       auto pWorld = g_WorldMgr->getWorld(0);
      // this->actor = pWorld->getActorMgr()->ToCastMgr()->spawnItem(WCoord(startPos.x, startPos.y, startPos.z), 13627, 1);
      // actor->setScale(100.0f);
      if (def.EventType == "sys_airdrop") {
        this->actor = ActorBoat::create(def.PlanceModelId, pWorld, startPos.x, startPos.y, startPos.z);
        actor->setScale(0.50f);
      } else if (def.EventType == "player_airdrop") {
        this->actor = ActorBoat::create(def.PlanceModelId, pWorld, startPos.x, startPos.y, startPos.z);
        actor->setScale(0.50f);
      }
      if (actor)
      {
          objId = actor->getObjId();
          SoundComponent* soundcompont = actor->getSoundComponent();
          soundcompont->playLoopSound(SoundPath,1,1);
      }
    }
}

AirDropCraft::~AirDropCraft(){
    if (g_WorldMgr && g_WorldMgr->getWorld(0)) {
        auto pWorld = g_WorldMgr->getWorld(0);
        // 检查item指针是否有效，避免野指针异常
        if (this->actor && isActorExist()) {
            this->actor->setNeedClear();
        }
    }

    if (!IsFinished()) {
        sendBroadCastPos("timeout");
    }
}

void AirDropCraft::SetDropPoint(const Rainbow::Vector3f& point) {

    LOG_INFO("AirDropCraft::SetDropPoint position (%d, %d, %d)", point.x, point.y, point.z);

    m_dropPoint = point;
    // 计算起点到投放点的方向和距离
    Rainbow::Vector3f deltaToDropPoint = m_dropPoint - m_startPos;
    m_distanceToDropPoint = deltaToDropPoint.Length();

    if (m_distanceToDropPoint > 0.0001f) {
        m_directionToDropPoint.x = deltaToDropPoint.x / m_distanceToDropPoint;
        m_directionToDropPoint.y = 0.0f;  // 保持水平飞行
        m_directionToDropPoint.z = deltaToDropPoint.z / m_distanceToDropPoint;
    }

    // 计算投放点到终点的方向和距离
    Rainbow::Vector3f deltaFromDropPoint = m_endPos - m_dropPoint;
    m_distanceFromDropPoint = deltaFromDropPoint.Length();

    if (m_distanceFromDropPoint > 0.0001f) {
        m_directionFromDropPoint.x = deltaFromDropPoint.x / m_distanceFromDropPoint;
        m_directionFromDropPoint.y = 0.0f;
        m_directionFromDropPoint.z = deltaFromDropPoint.z / m_distanceFromDropPoint;
    }

    // 总距离是两段距离之和
    m_totalDistance = m_distanceToDropPoint + m_distanceFromDropPoint;
    m_dropPointDistance = m_distanceToDropPoint;
}

void AirDropCraft::Update(float deltaTime) {
    if (m_isFinished) return;

    if (m_startTime==0.0){
        sendBroadCastPos("start");
    }

    m_startTime += deltaTime;

    // 检查飞机实体是否有效
    if (actor && isActorExist()) {
        // 更新位置
        UpdatePosition(deltaTime);
    }

    // 每5s一次
    if(m_startTime - m_lastTime > 5 ){
        sendBroadCastPos("flying");
        m_lastTime = m_startTime;
     }

    // 检查投放点
    CheckDropPoint();

    // 检查是否到达终点
    if (m_isReachedDropPoint && m_currentDistance >= m_totalDistance) {
        m_isFinished = true;
        StopEffects();
        sendBroadCastPos("end");
    }

}

void AirDropCraft::UpdatePosition(float deltaTime) {

    Rainbow::Vector3f direction;
    float moveDistance = m_speed * deltaTime;
    m_currentDistance += moveDistance;

    // 计算当前位置，根据是否到达投放点使用不同的计算方法
    if (!m_isReachedDropPoint) {
        // 未到达投放点，从起点向投放点飞行
        direction = m_directionToDropPoint;

        // 从起点出发计算位置
        // 注意：不限制m_currentDistance，以便正确判断是否到达投放点和终点
        float actualDistance = m_currentDistance;
        if (actualDistance > m_distanceToDropPoint) {
            // 仅在计算位置时限制距离，不修改m_currentDistance
            actualDistance = m_distanceToDropPoint;
        }
        m_currentPos = m_startPos + direction * actualDistance;
    } else {
        // 已到达投放点，从投放点向终点飞行
        direction = m_directionFromDropPoint;

        // 计算从投放点出发已飞行的距离
        float distanceFromDropPoint = m_currentDistance - m_distanceToDropPoint;

        // 仅在计算位置时限制距离，不修改m_currentDistance
        float actualDistanceFromDropPoint = distanceFromDropPoint;
        if (actualDistanceFromDropPoint > m_distanceFromDropPoint) {
            actualDistanceFromDropPoint = m_distanceFromDropPoint;
        }

        // 从投放点出发计算位置
        m_currentPos = m_dropPoint + direction * actualDistanceFromDropPoint;
    }

    m_currentPos.y = m_startPos.y;  // 保持初始高度

    // 检查item指针是否有效，避免野指针异常
    if (actor && isActorExist()) {
        ActorLocoMotion* locmove = actor->getLocoMotion();
        if (locmove) {
            // 计算朝向角度（使飞机朝向飞行方向）
            // 使用当前的飞行方向向量来计算偏航角
            float yaw = atan2(direction.x, direction.z) * 180.0f / M_PI;

            // 使用 gotoPosition 的三个参数版本来同时设置位置和旋转
            locmove->gotoPosition(m_currentPos, yaw, 0.0f);
            actor->update(deltaTime);
        }
    }
}

void AirDropCraft::CheckDropPoint() {
    if (!m_isReachedDropPoint && m_currentDistance >= m_dropPointDistance) {
        m_isReachedDropPoint = true;
        sendBroadCastPos("drop");
    }
}

void AirDropCraft::PlayEffects() {
    // if (!m_visualEffectId.empty()) {
    //     // 播放视觉特效
    //     EffectSystem::GetInstance().PlayVisualEffect(
    //         m_visualEffectId,
    //         m_currentPos
    //     );
    // }

    // if (!m_soundEffectId.empty()) {
    //     // 播放音效
    //     EffectSystem::GetInstance().PlaySoundEffect(
    //         m_soundEffectId,
    //         m_currentPos
    //     );
    // }
}

void AirDropCraft::StopEffects() {
    // if (!m_visualEffectId.empty()) {
    //     EffectSystem::GetInstance().StopVisualEffect(m_visualEffectId);
    // }

    // if (!m_soundEffectId.empty()) {
    //     EffectSystem::GetInstance().StopSoundEffect(m_soundEffectId);
    // }
}
bool AirDropCraft::isActorExist(){

    auto pWorld = g_WorldMgr->getWorld(0);
    // 检查item指针是否有效，避免野指针异常
    if (pWorld && pWorld->getActorMgr()){
        ClientActorMgr* actormgr = pWorld->getActorMgr()->ToCastMgr();
        return actormgr->isActorExist(objId);   
    }
    return false;
}


static std::string ToStr(float i) {
    int v = i / 100;
    return std::to_string(v);
}
void AirDropCraft::sendBroadCastPos(string eventType) {
    PB_AirDrop_Event pbData;
    pbData.set_eventid(m_event_def.ID);
    pbData.set_eventtype(eventType);
    pbData.set_eventname(m_event_def.Name);
    pbData.set_start_pos_x(m_startPos.x);
    pbData.set_start_pos_y(m_startPos.y);
    pbData.set_start_pos_z(m_startPos.z);
    pbData.set_end_pos_x(m_endPos.x);
    pbData.set_end_pos_y(m_endPos.y);
    pbData.set_end_pos_z(m_endPos.z);
    pbData.set_drop_pos_x(m_dropPoint.x);
    pbData.set_drop_pos_y(m_dropPoint.y);
    pbData.set_drop_pos_z(m_dropPoint.z);
    pbData.set_current_pos_x(m_currentPos.x);
    pbData.set_current_pos_y(m_currentPos.y);
    pbData.set_current_pos_z(m_currentPos.z);
    pbData.set_timestamp(time(NULL));


    if (GetGameNetManagerPtr()) {
        GetGameNetManagerPtr()->sendBroadCast(PB_AIRDROP_EVENT_HC, pbData);
    }


    if (m_event_def.IsDebug) {
        if (GetIClientGameManagerInterface()) {

            auto game = GetIClientGameManagerInterface()->getICurGame(GameType::SurviveGame);

            if (game) {
                std::string msg = eventType + " Pos: " + ToStr(m_currentPos.x) + "," + ToStr(m_currentPos.y) + "," + ToStr(m_currentPos.z);

                game->sendChat(msg.c_str(), 1, 0); //广播空投消息
            }
        }
    }
}



